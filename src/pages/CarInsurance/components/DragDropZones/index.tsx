import type { ImagePreviewInstance } from '@/components/ImagePreview';
import ImagePreview from '@/components/ImagePreview';
import type { DragEndEvent, DragStartEvent } from '@dnd-kit/core';
import {
  closestCenter,
  DndContext,
  DragOverlay,
  KeyboardSensor,
  MouseSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { Col, Row, Tag } from 'antd';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useCarInsuranceBulkOcrContext } from '../../context/CarInsuranceBulkOcrContext';
import { getFileNameFromUrl, getOssPathFromUrl, getRandomUUID } from '../../utils';
import DraggableItem from './DraggableItem';
import './index.less';
import SidebarList from './SidebarList';
import SourceArea from './SourceArea';
import TargetZone from './TargetZone';

export interface DragItem {
  id: string;
  name: string;
  type?: 'image' | 'file';
  size?: string;
  url?: string; // 添加文件URL用于预览
  filePath?: string; // oss短路径
}

export interface ListItem {
  id: string;
  vin: string;
  count: number;
  plateNo: string;
}

interface DragDropZonesProps {
  onDataChange?: (data: {
    globalSourceFiles: DragItem[]; // 全局源文件（notMatchFileDTOList）
    currentCategoryData: {
      targetZone1: DragItem[];
      targetZone2: DragItem[];
    };
    formattedApiData?: Record<
      string,
      {
        vehicleLicenseUrlList: string[];
        vehicleLicenseOssUrlList: string[];
        insuranceSlipUrlList: string[];
        insuranceSlipOssUrlList: string[];
      }
    >;
  }) => void;
}

// 将文件item转换为DragItem格式
const convertUrlToDragItem = useCallback((item: any, index: number): DragItem => {
  const fileName = getFileNameFromUrl(item?.url);
  return {
    id: `${item?.id}_${index}`, // 使用URL和索引作为唯一ID
    name: fileName,
    type: fileName.toLowerCase().includes('.pdf') ? 'file' : 'image',
    url: item?.url,
    filePath: item?.filePath || getOssPathFromUrl(item?.url || ''), // 如果没有filePath，从URL中提取
  };
}, []);

const DragDropZones: React.FC<DragDropZonesProps> = ({ onDataChange }) => {
  // 获取批量OCR上下文数据
  const { batchOcrData } = useCarInsuranceBulkOcrContext();
  // 根据carInfoDTOList格式化数据
  const formatCarInfoData = useCallback(() => {
    const categoryDataMap: Record<
      string,
      {
        targetZone1: DragItem[];
        targetZone2: DragItem[];
      }
    > = {};

    const listItems: ListItem[] = [];

    // 处理carInfoDTOList中的每个车辆
    batchOcrData.carInfoDTOList?.forEach((carInfo: any) => {
      const vin = carInfo.vin || '无车架号';
      const plateNo = carInfo.plateNo || '无车牌号';

      // 处理车辆证件 (vehicleLicenseOssUrlList -> targetZone1)
      // 如果是团单(groupFlag=1)，需要为每个车辆生成唯一的id，避免重复
      const vehicleDocuments =
        carInfo.vehicleLicenseInfoList?.map((item: any, index: number) => {
          const uniqueId =
            batchOcrData.groupFlag === 1
              ? `${item?.filePath}_${carInfo?.id}_${index}_${getRandomUUID(8)}` // 团单时生成唯一ID
              : `${item?.filePath}_${index}`; // 非团单时使用原逻辑

          const fileName = getFileNameFromUrl(item?.url);
          return {
            id: uniqueId,
            name: fileName,
            type: fileName.toLowerCase().includes('.pdf') ? 'file' : 'image',
            url: item?.url,
            filePath: item?.filePath,
          } as DragItem;
        }) || [];

      // 处理投保材料 (insuranceSlipOssUrlList -> targetZone2)
      const insuranceMaterials =
        carInfo.insuranceSlipInfoList?.map((item: any, index: number) => {
          const uniqueId =
            batchOcrData.groupFlag === 1
              ? `${item?.filePath}_${carInfo?.id}_${index}_${getRandomUUID(8)}` // 团单时生成唯一ID
              : `${item?.filePath}_${index}`; // 非团单时使用原逻辑

          const fileName = getFileNameFromUrl(item?.url);
          return {
            id: uniqueId,
            name: fileName,
            type: fileName.toLowerCase().includes('.pdf') ? 'file' : 'image',
            url: item?.url,
            filePath: item?.filePath,
          } as DragItem;
        }) || [];

      // 初始化该VIN的数据结构（移除sourceFiles字段）
      categoryDataMap[carInfo.id] = {
        targetZone1: vehicleDocuments, // 车辆证件
        targetZone2: insuranceMaterials, // 投保材料
      };

      // 添加到列表项
      listItems.push({
        id: carInfo.id,
        vin: vin,
        plateNo: plateNo,
        count: vehicleDocuments.length + insuranceMaterials.length,
      });
    });

    // 处理未匹配的文件作为全局源文件
    const globalSourceFiles: DragItem[] = [];
    batchOcrData.notMatchFileDTOList?.forEach((file: any, index: number) => {
      if (file.url) {
        globalSourceFiles.push(convertUrlToDragItem(file, index));
      }
    });

    return { categoryDataMap, listItems, globalSourceFiles };
  }, [batchOcrData, convertUrlToDragItem]);

  // 获取格式化后的数据
  const {
    categoryDataMap: formattedCategoryDataMap,
    listItems: formattedListItems,
    globalSourceFiles: formattedGlobalSourceFiles,
  } = useMemo(() => {
    return formatCarInfoData();
  }, [formatCarInfoData]);

  const [selectedListId, setSelectedListId] = useState<string | null>(
    formattedListItems.length > 0 ? formattedListItems[0].id : null,
  );
  const [activeId, setActiveId] = useState<string | null>(null);

  // 预览相关
  const previewRef = useRef<ImagePreviewInstance>(null);

  // 所有分类的数据状态 - 使用格式化后的数据初始化（移除sourceFiles字段）
  const [categoryDataMap, setCategoryDataMap] = useState<
    Record<
      string,
      {
        targetZone1: DragItem[];
        targetZone2: DragItem[];
      }
    >
  >(formattedCategoryDataMap);

  // 全局源文件状态 - 用于显示notMatchFileDTOList
  const [globalSourceFiles, setGlobalSourceFiles] = useState<DragItem[]>(
    formattedGlobalSourceFiles,
  );

  // 初始化标记，确保只同步一次
  const [hasInitialSync, setHasInitialSync] = useState(false);

  // 缓存格式化数据的结果，避免重复计算
  const formatDataCacheRef = useRef<{
    lastCategoryDataMap: any;
    lastResult: any;
    lastHash: string;
  }>({ lastCategoryDataMap: null, lastResult: null, lastHash: '' });

  // 防抖函数，优化数据同步性能 - 增加防抖时间以减少频繁计算
  const debounceTimeoutRef = useRef<NodeJS.Timeout>();
  const debouncedDataSync = useCallback(
    (data: any) => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      debounceTimeoutRef.current = setTimeout(() => {
        onDataChange?.(data);
      }, 100); // 增加防抖时间到100ms，减少频繁的数据同步
    },
    [onDataChange],
  );

  // 更新categoryDataMap和globalSourceFiles当格式化数据变化时
  useEffect(() => {
    setCategoryDataMap(formattedCategoryDataMap);
    setGlobalSourceFiles(formattedGlobalSourceFiles);
  }, [formattedCategoryDataMap, formattedGlobalSourceFiles]);

  // 组件卸载时清除数据和定时器
  useEffect(() => {
    return () => {
      console.log('组件卸载');
      // 清除防抖定时器
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      // 清除格式化数据缓存
      formatDataCacheRef.current = { lastCategoryDataMap: null, lastResult: null, lastHash: '' };
      // 清除一些旧数据等
      setCategoryDataMap({});
      setGlobalSourceFiles([]);
      setSelectedListId(null);
      setActiveId(null);
    };
  }, []);

  // 分类列表项（使用格式化后的数据）
  const listItems = useMemo(() => {
    return formattedListItems.map((item) => ({
      ...item,
      count:
        // (categoryDataMap[item.id]?.sourceFiles.length || 0) +
        (categoryDataMap[item.id]?.targetZone1.length || 0) +
        (categoryDataMap[item.id]?.targetZone2.length || 0),
    }));
  }, [formattedListItems, categoryDataMap]);

  // 格式化数据为API提交格式 - 优化性能，使用更智能的缓存策略
  const formatDataForSubmission = useCallback((new_categoryDataMap) => {
    // 生成数据的简单哈希值用于快速比较
    const dataHash = JSON.stringify(
      Object.keys(new_categoryDataMap)
        .sort()
        .map((key) => ({
          key,
          zone1Count: new_categoryDataMap[key]?.targetZone1?.length || 0,
          zone2Count: new_categoryDataMap[key]?.targetZone2?.length || 0,
        })),
    );

    // 检查是否可以使用缓存结果
    if (formatDataCacheRef.current.lastHash === dataHash) {
      return formatDataCacheRef.current.lastResult;
    }

    const formattedData: Record<
      string,
      {
        vehicleLicenseUrlList: string[];
        vehicleLicenseOssUrlList: string[];
        insuranceSlipUrlList: string[];
        insuranceSlipOssUrlList: string[];
      }
    > = {};

    // 使用for...in循环替代Object.keys().forEach，性能更好
    for (const id in new_categoryDataMap) {
      if (!new_categoryDataMap.hasOwnProperty(id)) continue;

      const categoryData = new_categoryDataMap[id];

      // 使用更高效的方式提取数据
      const vehicleLicenseOssUrlList: string[] = [];
      const vehicleLicenseUrlList: string[] = [];
      const insuranceSlipOssUrlList: string[] = [];
      const insuranceSlipUrlList: string[] = [];

      // 一次遍历提取所有需要的数据
      for (const item of categoryData.targetZone1) {
        if (item.filePath) vehicleLicenseOssUrlList.push(item.filePath);
        if (item.url) vehicleLicenseUrlList.push(item.url);
      }

      for (const item of categoryData.targetZone2) {
        if (item.filePath) insuranceSlipOssUrlList.push(item.filePath);
        if (item.url) insuranceSlipUrlList.push(item.url);
      }

      formattedData[id] = {
        vehicleLicenseUrlList,
        vehicleLicenseOssUrlList,
        insuranceSlipUrlList,
        insuranceSlipOssUrlList,
      };
    }

    // 缓存结果
    formatDataCacheRef.current = {
      lastCategoryDataMap: new_categoryDataMap,
      lastResult: formattedData,
      lastHash: dataHash,
    };

    return formattedData;
  }, []);

  // 初始化时同步数据到父组件（只执行一次）
  useEffect(() => {
    if (onDataChange && !hasInitialSync && Object.keys(formattedCategoryDataMap).length > 0) {
      // 获取第一个车辆的数据作为初始显示数据
      const firstCarId = Object.keys(formattedCategoryDataMap)[0];
      const firstCarData = formattedCategoryDataMap[firstCarId] || {
        targetZone1: [],
        targetZone2: [],
      };

      // 格式化初始数据为API提交格式
      const formattedApiData = formatDataForSubmission(formattedCategoryDataMap);

      // 同步初始数据到父组件
      onDataChange({
        globalSourceFiles: formattedGlobalSourceFiles,
        currentCategoryData: firstCarData,
        formattedApiData,
      });
      // 标记已完成初始同步
      setHasInitialSync(true);
    }
  }, [
    formattedCategoryDataMap,
    formattedGlobalSourceFiles,
    onDataChange,
    formatDataForSubmission,
    hasInitialSync,
  ]);

  // 预览事件监听 - 优化事件处理和错误处理
  useEffect(() => {
    const handlePreviewFile = (event: CustomEvent) => {
      console.log('预览事件触发:', event.detail);
      const { url, fileName } = event.detail;
      if (previewRef.current) {
        try {
          previewRef.current.previewFile({
            url,
            fileName,
          });
        } catch (error) {
          console.error('预览文件时出错:', error);
        }
      }
    };

    window.addEventListener('drag-item-preview-file', handlePreviewFile as EventListener);

    return () => {
      console.log('移除预览事件监听');
      window.removeEventListener('drag-item-preview-file', handlePreviewFile as EventListener);
    };
  }, []);

  // 配置拖拽传感器，避免点击就触发拖拽，优化性能
  // 关键：delay和tolerance对于避免事件冲突非常重要
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 10, // 增加拖拽距离阈值，减少误触
        delay: 100, // 延迟很重要！避免与click事件冲突
        tolerance: 8, // 容差避免意外激活
      },
    }),
    useSensor(MouseSensor, {
      activationConstraint: {
        distance: 10, // 统一拖拽距离阈值
        delay: 100, // 统一延迟时间，确保click事件优先
        tolerance: 8, // 增加容差
      },
    }),
    useSensor(KeyboardSensor),
  );

  // 获取当前选中分类的数据 - 使用useMemo优化性能，避免不必要的重新计算
  const getCurrentCategoryData = useMemo(() => {
    if (!selectedListId || !categoryDataMap[selectedListId]) {
      return { targetZone1: [], targetZone2: [] };
    }
    // 深度比较，只有数据真正变化时才返回新对象
    const currentData = categoryDataMap[selectedListId];
    return {
      targetZone1: currentData.targetZone1,
      targetZone2: currentData.targetZone2,
    };
  }, [selectedListId, categoryDataMap]);

  // 找到项目所在的区域 - 优化查找性能，使用Map缓存和更高效的遍历
  const itemLocationMap = useMemo(() => {
    const map = new Map<string, string>();

    // 使用for循环替代forEach，性能更好
    const globalSourceLength = globalSourceFiles.length;
    for (let i = 0; i < globalSourceLength; i++) {
      map.set(globalSourceFiles[i].id, 'source');
    }

    // 缓存当前分类数据位置
    const currentData = getCurrentCategoryData;
    const targetZone1Length = currentData.targetZone1.length;
    const targetZone2Length = currentData.targetZone2.length;

    for (let i = 0; i < targetZone1Length; i++) {
      map.set(currentData.targetZone1[i].id, 'targetZone1');
    }
    for (let i = 0; i < targetZone2Length; i++) {
      map.set(currentData.targetZone2[i].id, 'targetZone2');
    }

    return map;
  }, [getCurrentCategoryData, globalSourceFiles]);

  const findItemLocation = useCallback(
    (id: string) => {
      return itemLocationMap.get(id) || null;
    },
    [itemLocationMap],
  );

  // 获取拖拽的项目 - 优化查找性能
  const getActiveItem = useCallback(() => {
    if (!activeId) return null;

    const currentData = getCurrentCategoryData;

    // 首先从全局源文件中查找
    const globalSourceItem = globalSourceFiles.find((item) => item.id === activeId);
    if (globalSourceItem) return globalSourceItem;

    // 从目标区域中查找
    const targetItem1 = currentData.targetZone1.find((item) => item.id === activeId);
    if (targetItem1) return targetItem1;

    const targetItem2 = currentData.targetZone2.find((item) => item.id === activeId);
    if (targetItem2) return targetItem2;

    return null;
  }, [activeId, getCurrentCategoryData, globalSourceFiles]);

  // 开始拖拽 - 添加性能监控
  const handleDragStart = (event: DragStartEvent) => {
    console.time('drag-operation');
    setActiveId(event.active.id as string);
  };

  // 结束拖拽 - 优化性能，减少不必要的计算
  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event;

      setActiveId(null);

      if (!over || !selectedListId) {
        return;
      }

      const c_activeId = active.id as string;
      const overId = over.id as string;

      // 如果拖拽到同一个位置，不做任何操作
      const sourceLocation = findItemLocation(c_activeId);
      if (sourceLocation === overId) {
        return;
      }

      // 提前获取当前数据，避免在回调中重复计算
      const currentData = getCurrentCategoryData;
      let draggedItem: DragItem | undefined;

      // 找到被拖拽的项目
      if (sourceLocation === 'source') {
        // 从全局源文件中查找
        draggedItem = globalSourceFiles.find((item) => item.id === c_activeId);
      } else if (sourceLocation === 'targetZone1') {
        draggedItem = currentData.targetZone1.find((item) => item.id === c_activeId);
      } else if (sourceLocation === 'targetZone2') {
        draggedItem = currentData.targetZone2.find((item) => item.id === c_activeId);
      }

      // 只有在找到项目且目标区域有效时才进行移动
      if (
        draggedItem &&
        (overId === 'source' || overId === 'targetZone1' || overId === 'targetZone2')
      ) {
        // 判断是否从全局源文件拖拽
        const isFromGlobalSource = sourceLocation === 'source';

        if (isFromGlobalSource) {
          // 从全局源文件拖拽到目标区域
          if (overId !== 'source') {
            setGlobalSourceFiles((prev) => prev.filter((item) => item.id !== c_activeId));
          }
        }

        // 使用React的批量更新和更高效的状态管理
        const performDragOperation = () => {
          const newData = { ...currentData };
          let updatedGlobalSourceFiles = globalSourceFiles;
          let needsGlobalSourceUpdate = false;

          // 从原位置移除（仅处理目标区域之间的移动）
          if (sourceLocation === 'targetZone1') {
            newData.targetZone1 = newData.targetZone1.filter((item) => item.id !== c_activeId);
          } else if (sourceLocation === 'targetZone2') {
            newData.targetZone2 = newData.targetZone2.filter((item) => item.id !== c_activeId);
          }

          // 添加到新位置并计算全局源文件状态
          if (overId === 'targetZone1') {
            newData.targetZone1 = [...newData.targetZone1, draggedItem];
            if (isFromGlobalSource) {
              updatedGlobalSourceFiles = globalSourceFiles.filter((item) => item.id !== c_activeId);
              needsGlobalSourceUpdate = true;
            }
          } else if (overId === 'targetZone2') {
            newData.targetZone2 = [...newData.targetZone2, draggedItem];
            if (isFromGlobalSource) {
              updatedGlobalSourceFiles = globalSourceFiles.filter((item) => item.id !== c_activeId);
              needsGlobalSourceUpdate = true;
            }
          } else if (overId === 'source') {
            // 拖拽回源区域时，添加到全局源文件
            updatedGlobalSourceFiles = [...globalSourceFiles, draggedItem];
            needsGlobalSourceUpdate = true;
          }

          // 使用React.unstable_batchedUpdates确保批量更新（如果可用）
          const batchedUpdate = () => {
            const updatedCategoryData = {
              ...categoryDataMap,
              [selectedListId]: newData,
            };

            setCategoryDataMap(updatedCategoryData);

            if (needsGlobalSourceUpdate) {
              setGlobalSourceFiles(updatedGlobalSourceFiles);
            }

            // 异步触发数据变更回调，避免阻塞UI
            requestAnimationFrame(() => {
              const formattedApiData = formatDataForSubmission(updatedCategoryData);
              debouncedDataSync({
                globalSourceFiles: updatedGlobalSourceFiles,
                currentCategoryData: {
                  targetZone1: newData.targetZone1,
                  targetZone2: newData.targetZone2,
                },
                formattedApiData,
              });
            });
          };

          // 尝试使用React的批量更新，如果不可用则直接执行
          if (typeof (React as any).unstable_batchedUpdates === 'function') {
            (React as any).unstable_batchedUpdates(batchedUpdate);
          } else {
            batchedUpdate();
          }
        };

        performDragOperation();
        console.timeEnd('drag-operation');
      }
    },
    [
      selectedListId,
      findItemLocation,
      getCurrentCategoryData,
      globalSourceFiles,
      formatDataForSubmission,
      debouncedDataSync,
    ],
  );

  // 删除目标区域中的项目
  const handleDeleteItem = useCallback(
    (itemId: string) => {
      if (!selectedListId) return;

      setCategoryDataMap((prev) => {
        const currentData = prev[selectedListId];
        let updatedData = { ...currentData };

        // 检查是否在目标区域1中
        if (currentData.targetZone1.find((item) => item.id === itemId)) {
          updatedData = {
            ...updatedData,
            targetZone1: currentData.targetZone1.filter((item) => item.id !== itemId),
          };
        }
        // 检查是否在目标区域2中
        else if (currentData.targetZone2.find((item) => item.id === itemId)) {
          updatedData = {
            ...updatedData,
            targetZone2: currentData.targetZone2.filter((item) => item.id !== itemId),
          };
        }

        const newCategoryDataMap = {
          ...prev,
          [selectedListId]: updatedData,
        };

        // 触发数据变更回调，包含格式化后的API数据 - 使用防抖优化
        const formattedApiData = formatDataForSubmission(newCategoryDataMap);
        debouncedDataSync({
          globalSourceFiles,
          currentCategoryData: {
            targetZone1: updatedData.targetZone1,
            targetZone2: updatedData.targetZone2,
          },
          formattedApiData,
        });

        return newCategoryDataMap;
      });
    },
    [selectedListId, onDataChange, globalSourceFiles, formatDataForSubmission],
  );

  return (
    <div className="drag-drop-zones-new">
      <DndContext
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        sensors={sensors}
      >
        {/* 上方：源文件区域 */}
        <div className="top-section">
          <SourceArea items={globalSourceFiles} />
        </div>

        {/* 下方：左侧列表 + 右侧目标区域 */}
        <div className="bottom-section">
          <Row gutter={16}>
            {/* 左侧：分类列表 */}
            <Col span={8}>
              <SidebarList
                items={listItems}
                selectedId={selectedListId}
                onSelect={setSelectedListId}
              />
            </Col>

            {/* 右侧：两个目标区域 */}
            <Col span={16}>
              <div className="target-zones">
                <div className="target-zones-header">
                  <span>当前车架号：</span>
                  {selectedListId && (
                    <Tag color="orange">
                      {listItems.find((item) => item.id === selectedListId)?.vin}
                    </Tag>
                  )}
                </div>
                <Row gutter={16}>
                  <Col span={12}>
                    <TargetZone
                      id="targetZone1"
                      title="车辆证件"
                      items={getCurrentCategoryData.targetZone1}
                      onDeleteItem={handleDeleteItem}
                    />
                  </Col>
                  <Col span={12}>
                    <TargetZone
                      id="targetZone2"
                      title="投保材料"
                      items={getCurrentCategoryData.targetZone2}
                      onDeleteItem={handleDeleteItem}
                    />
                  </Col>
                </Row>
              </div>
            </Col>
          </Row>
        </div>

        {/* 拖拽预览层 */}
        <DragOverlay>
          {activeId ? (
            <div className="drag-overlay-new">
              <DraggableItem item={getActiveItem()!} isDragging />
            </div>
          ) : null}
        </DragOverlay>
      </DndContext>

      {/* 文件预览组件 - 不占用布局空间 */}
      <ImagePreview ref={previewRef} />
    </div>
  );
};

export default DragDropZones;
