import { useDroppable } from '@dnd-kit/core';
import { Button, Card, Tag } from 'antd';
import React, { useMemo, useState } from 'react';
import { DragItem } from '../DragDropZones';
import DraggableItem from './DraggableItem';

interface TargetZoneProps {
  id: string;
  title: string;
  items: DragItem[];
  onDeleteItem: (itemId: string) => void;
}

// 目标拖放区域组件（右侧）- 优化性能版本，支持分页查看
const TargetZone: React.FC<TargetZoneProps> = ({ id, title, items, onDeleteItem }) => {
  const { isOver, setNodeRef } = useDroppable({
    id,
  });

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [showAll, setShowAll] = useState(false);
  const itemsPerPage = 50;

  // 当文件数量变化时，检查当前页是否还有效
  React.useEffect(() => {
    const totalPages = Math.ceil(items.length / itemsPerPage);
    if (currentPage > totalPages && totalPages > 0) {
      setCurrentPage(totalPages);
    }
  }, [items.length, currentPage, itemsPerPage]);

  // 使用useMemo优化渲染性能，支持分页和显示全部
  const renderedContent = useMemo(() => {
    if (items.length === 0) {
      return <div className="empty-target">拖拽文件到此区域</div>;
    }

    // 如果选择显示全部，则显示所有文件
    if (showAll) {
      return (
        <div className="target-files-list">
          {items.map((item) => (
            <DraggableItem key={item.id} item={item} onDelete={onDeleteItem} isSource={false} />
          ))}
          <div className="pagination-controls">
            <div className="pagination-info">显示全部 {items.length} 个文件</div>
            <div className="pagination-buttons">
              <Button
                size="small"
                onClick={() => {
                  setShowAll(false);
                  setCurrentPage(1);
                }}
              >
                返回分页模式
              </Button>
            </div>
          </div>
        </div>
      );
    }

    // 分页模式
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const visibleItems = items.slice(startIndex, endIndex);
    const hasMore = items.length > endIndex;
    const hasPrevious = currentPage > 1;
    const totalPages = Math.ceil(items.length / itemsPerPage);

    return (
      <div className="target-files-list">
        {visibleItems.map((item) => (
          <DraggableItem key={item.id} item={item} onDelete={onDeleteItem} isSource={false} />
        ))}
        {(hasMore || hasPrevious || items.length > itemsPerPage) && (
          <div className="pagination-controls">
            <div className="pagination-info">
              第 {currentPage} 页，共 {totalPages} 页
            </div>
            <div className="pagination-buttons">
              {hasPrevious && (
                <Button size="small" onClick={() => setCurrentPage((prev) => prev - 1)}>
                  上一页
                </Button>
              )}
              {hasMore && (
                <Button
                  size="small"
                  type="primary"
                  onClick={() => setCurrentPage((prev) => prev + 1)}
                >
                  下一页 ({items.length - endIndex} 个)
                </Button>
              )}
              {items.length > itemsPerPage && (
                <Button
                  size="small"
                  type="dashed"
                  onClick={() => setShowAll(true)}
                  title="显示全部文件"
                >
                  显示全部
                </Button>
              )}
            </div>
          </div>
        )}
      </div>
    );
  }, [items, onDeleteItem, currentPage, showAll]);

  // 使用useMemo优化CSS类名计算
  const containerClassName = useMemo(() => {
    return `target-zone ${isOver ? 'target-over' : ''}`;
  }, [isOver]);

  return (
    <div className={containerClassName}>
      <Card
        title={
          <div className="zone-header">
            <span>{title}</span>
            <Tag color="green">{items.length}</Tag>
          </div>
        }
        size="small"
      >
        <div ref={setNodeRef} className="target-drop-area">
          {renderedContent}
        </div>
      </Card>
    </div>
  );
};

export default React.memo(TargetZone);
